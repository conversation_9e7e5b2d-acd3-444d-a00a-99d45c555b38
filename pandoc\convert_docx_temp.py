#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from docx import Document

def convert_docx_to_txt(docx_path, txt_path):
    """Convert a .docx file to .txt file"""
    try:
        # Load the document
        doc = Document(docx_path)
        
        # Extract text from all paragraphs
        full_text = []
        for paragraph in doc.paragraphs:
            full_text.append(paragraph.text)
        
        # Write to text file with UTF-8 encoding
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(full_text))
        
        print(f"Successfully converted {docx_path} to {txt_path}")
        return True
        
    except Exception as e:
        print(f"Error converting {docx_path}: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python convert_docx_temp.py <input.docx> <output.txt>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    convert_docx_to_txt(input_file, output_file)
