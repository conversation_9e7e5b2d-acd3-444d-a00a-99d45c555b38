#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để tạo file tóm tắt các nội dung chính, mạch truyện quan trọng
"""

import os
import re
from datetime import datetime

def read_chapter(chapter_num):
    """Đọc nội dung một chương"""
    filename = f"Chương {chapter_num}.txt"
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        return content
    except FileNotFoundError:
        return ""
    except Exception as e:
        return ""

def extract_key_info(chapter_content, chapter_num):
    """Trích xuất thông tin quan trọng từ một chương"""
    lines = chapter_content.split('\n')

    # Tìm tiêu đề chương
    title = ""
    if lines:
        title = lines[0].strip()

    # Tìm bối cảnh
    setting = ""
    for line in lines:
        if line.startswith("Bối cảnh:"):
            setting = line.replace("Bối cảnh:", "").strip()
            break

    # Tạo tóm tắt thủ công dựa trên nội dung
    key_events = []
    character_development = []
    worldbuilding = []

    # Phân tích nội dung theo đoạn văn
    paragraphs = [p.strip() for p in chapter_content.split('\n') if p.strip() and not p.startswith('Chương') and not p.startswith('Bối cảnh:')]

    # Lọc các đoạn quan trọng (dài hơn 100 ký tự)
    important_paragraphs = [p for p in paragraphs if len(p) > 100]

    # Lấy các sự kiện chính (3-5 đoạn đầu quan trọng)
    for i, para in enumerate(important_paragraphs[:5]):
        if any(keyword in para.lower() for keyword in [
            'thức tỉnh', 'nguyên linh', 'chiến đấu', 'thi đấu', 'học viện',
            'đột phá', 'cảnh giới', 'gặp gỡ', 'quyết định', 'khám phá'
        ]):
            # Rút gọn đoạn văn
            if len(para) > 200:
                para = para[:200] + "..."
            key_events.append(para)

    # Lấy thông tin về nhân vật (tìm đoạn có tên nhân vật và hành động)
    for para in important_paragraphs:
        if any(name in para.lower() for name in ['vân nam', 'hắc vân thanh', 'chu tiểu linh']) and \
           any(action in para.lower() for action in ['cảm thấy', 'nghĩ', 'quyết định', 'nhận ra', 'hiểu']):
            if len(para) > 150:
                para = para[:150] + "..."
            character_development.append(para)
            if len(character_development) >= 2:
                break

    # Lấy thông tin worldbuilding (hệ thống tu luyện, địa danh, v.v.)
    for para in important_paragraphs:
        if any(world in para.lower() for world in [
            'nguyên đồ', 'nguyên sĩ', 'nguyên sư', 'cảnh giới', 'tu luyện',
            'học viện', 'gia tộc', 'đế quốc', 'thành', 'lãnh địa'
        ]):
            if len(para) > 150:
                para = para[:150] + "..."
            worldbuilding.append(para)
            if len(worldbuilding) >= 2:
                break

    return {
        'title': title,
        'setting': setting,
        'key_events': key_events[:3],
        'character_development': character_development[:2],
        'worldbuilding': worldbuilding[:2]
    }

def create_chapter_summary(chapter_num):
    """Tạo tóm tắt cho một chương"""
    content = read_chapter(chapter_num)
    if not content:
        return f"CHƯƠNG {chapter_num}: [KHÔNG CÓ NỘI DUNG]\n"
    
    info = extract_key_info(content, chapter_num)
    
    summary = []
    summary.append(f"CHƯƠNG {chapter_num}: {info['title']}")
    summary.append("=" * 60)
    
    if info['setting']:
        summary.append(f"📍 BỐI CẢNH: {info['setting']}")
        summary.append("")
    
    if info['key_events']:
        summary.append("🔥 SỰ KIỆN QUAN TRỌNG:")
        for i, event in enumerate(info['key_events'], 1):
            summary.append(f"   {i}. {event}")
        summary.append("")
    
    if info['character_development']:
        summary.append("👤 PHÁT TRIỂN NHÂN VẬT:")
        for i, dev in enumerate(info['character_development'], 1):
            summary.append(f"   {i}. {dev}")
        summary.append("")
    
    if info['worldbuilding']:
        summary.append("🌍 WORLDBUILDING:")
        for i, world in enumerate(info['worldbuilding'], 1):
            summary.append(f"   {i}. {world}")
        summary.append("")
    
    summary.append("-" * 60)
    summary.append("")
    
    return "\n".join(summary)

def create_full_summary():
    """Tạo file tóm tắt hoàn chỉnh"""
    print("Đang tạo file tóm tắt các nội dung chính...")
    
    summary_content = []
    
    # Header
    summary_content.append("=" * 80)
    summary_content.append("HẮC ÁM CHI CHỦ - TÓM TẮT NỘI DUNG CHÍNH")
    summary_content.append("=" * 80)
    summary_content.append("")
    summary_content.append("📖 Tóm tắt các sự kiện quan trọng, mạch truyện chính")
    summary_content.append("🎯 Các chi tiết ảnh hưởng trực tiếp đến cốt truyện")
    summary_content.append("⚡ Phát triển nhân vật và worldbuilding quan trọng")
    summary_content.append("")
    summary_content.append(f"🕒 Được tạo tự động vào: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    summary_content.append("=" * 80)
    summary_content.append("")
    
    # Tóm tắt từng chương
    for i in range(1, 41):
        print(f"Đang phân tích Chương {i}...")
        chapter_summary = create_chapter_summary(i)
        summary_content.append(chapter_summary)
    
    # Ghi file
    output_file = "Hac_Am_Chi_Chu_Tom_Tat_Noi_Dung.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(summary_content))
        
        print(f"✓ Đã tạo thành công file tóm tắt: {output_file}")
        
        # Thống kê
        total_size = os.path.getsize(output_file)
        print(f"✓ Kích thước file: {total_size:,} bytes")
        
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = len(f.readlines())
        print(f"✓ Tổng số dòng: {lines:,}")
        
    except Exception as e:
        print(f"✗ Lỗi khi tạo file tóm tắt: {e}")

def main():
    create_full_summary()

if __name__ == "__main__":
    main()
