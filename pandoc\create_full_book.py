#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để tạo file tổng hợp đầy đủ 40 chương
"""

import os
from datetime import datetime

def create_table_of_contents():
    """Tạo mục lục cho file tổng hợp"""
    toc = []
    toc.append("=" * 80)
    toc.append("HẮC ÁM CHI CHỦ - TIỂU THUYẾT HOÀN CHỈNH")
    toc.append("=" * 80)
    toc.append("")
    toc.append("MỤC LỤC:")
    toc.append("-" * 40)
    
    for i in range(1, 41):
        toc.append(f"Chương {i:2d}")
    
    toc.append("")
    toc.append("=" * 80)
    toc.append(f"Được tạo tự động vào: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    toc.append("=" * 80)
    toc.append("")
    
    return "\n".join(toc)

def read_chapter(chapter_num):
    """Đọc nội dung một chương"""
    filename = f"Chương {chapter_num}.txt"
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        return content
    except FileNotFoundError:
        return f"[KHÔNG TÌM THẤY CHƯƠNG {chapter_num}]"
    except Exception as e:
        return f"[LỖI ĐỌC CHƯƠNG {chapter_num}: {e}]"

def create_full_book():
    """Tạo file tổng hợp hoàn chỉnh"""
    print("Đang tạo file tổng hợp 40 chương...")
    
    # Tạo mục lục
    full_content = [create_table_of_contents()]
    
    # Thêm từng chương
    for i in range(1, 41):
        print(f"Đang xử lý Chương {i}...")
        
        # Header cho chương
        chapter_header = f"\n\n{'='*80}\nCHƯƠNG {i}\n{'='*80}\n\n"
        full_content.append(chapter_header)
        
        # Nội dung chương
        chapter_content = read_chapter(i)
        full_content.append(chapter_content)
        
        # Footer cho chương
        chapter_footer = f"\n\n{'-'*60}\n[KẾT THÚC CHƯƠNG {i}]\n{'-'*60}\n"
        full_content.append(chapter_footer)
    
    # Ghi file
    output_file = "Hac_Am_Chi_Chu_Full_40_Chuong.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("".join(full_content))
        
        print(f"✓ Đã tạo thành công file: {output_file}")
        
        # Thống kê
        total_size = os.path.getsize(output_file)
        print(f"✓ Kích thước file: {total_size:,} bytes")
        
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = len(f.readlines())
        print(f"✓ Tổng số dòng: {lines:,}")
        
    except Exception as e:
        print(f"✗ Lỗi khi tạo file: {e}")

def main():
    create_full_book()

if __name__ == "__main__":
    main()
