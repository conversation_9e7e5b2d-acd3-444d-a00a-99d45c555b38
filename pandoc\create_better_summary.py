#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để tạo file tóm tắt tốt hơn với nội dung được phân tích thủ công
"""

import os
from datetime import datetime

def read_chapter(chapter_num):
    """Đọc nội dung một chương"""
    filename = f"Ch<PERSON>ơng {chapter_num}.txt"
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        return content
    except:
        return ""

def analyze_chapter_content(chapter_num):
    """Phân tích nội dung chương và tạo tóm tắt có ý nghĩa"""
    content = read_chapter(chapter_num)
    if not content:
        return None
    
    lines = content.split('\n')
    
    # Tìm tiêu đề và bối cảnh
    title = lines[0].strip() if lines else f"Chương {chapter_num}"
    setting = ""
    for line in lines:
        if line.startswith("<PERSON><PERSON><PERSON> cảnh:"):
            setting = line.replace("Bối cảnh:", "").strip()
            break
    
    # Phân tích dựa trên từ khóa và ngữ cảnh
    summary = {
        'title': title,
        'setting': setting,
        'key_events': [],
        'character_development': [],
        'worldbuilding': [],
        'important_details': []
    }
    
    # Phân tích cụ thể cho từng chương dựa trên nội dung thực tế
    if chapter_num == 1:
        summary.update({
            'key_events': [
                'Vân Nam (16 tuổi) tham gia nghi thức thức tỉnh Nguyên Linh tại Từ Đường gia tộc',
                'Hồi tưởng chị gái Hắc Vân Thanh thức tỉnh Hắc Ám Song Lang 3 năm trước, được Lăng Phong nhận làm đệ tử',
                'Vân Nam thức tỉnh Hắc Ám Chi Nguyên (thực lực thật) và Hắc Nanh (che giấu), ẩn giấu sức mạnh'
            ],
            'character_development': [
                'Vân Nam thể hiện sự khôn ngoan khi quyết định ẩn giấu thực lực thật',
                'Hình thành tham vọng trở thành Hắc Ám Chi Chủ, khiến Đại Lục Huyền Nguyên cúi đầu'
            ],
            'worldbuilding': [
                'Đế Quốc Vĩnh Dạ - vùng đất bóng tối ở cực Bắc, có chính sách cai trị tàn khốc',
                'Gia tộc Hắc Nguyệt từng huy hoàng với Thủy Tổ Hắc Thiên (Nguyên Tông), giờ suy tàn'
            ],
            'important_details': [
                'Khế Ước Bóng Đêm và Ảnh Giới - sức mạnh bí ẩn của Vân Nam',
                'Dấu ấn hình vòng xoáy hắc ám xuất hiện trên mu bàn tay phải'
            ]
        })
    elif chapter_num == 2:
        summary.update({
            'key_events': [
                'Gia tộc Hắc Nguyệt tổ chức tiệc mừng, các gia tộc khác đến chúc mừng',
                'Lôi Minh (Lôi Viêm) thức tỉnh Lôi Báo, Hắc Thiết Cương (Hắc Thiết) thức tỉnh Hắc Thiết Trọng Kiếm',
                'Bàn luận về kỳ thi tuyển chọn đệ tử Học Viện Tinh Võ tại Thành Vô Song sau 6 tháng'
            ],
            'character_development': [
                'Chu Tiểu Linh xuất hiện - con gái tộc trưởng Chu gia, thanh mai trúc mã của Vân Nam',
                'Vân Nam thể hiện sự bình tĩnh, không bị ảnh hưởng bởi thành tích của đối thủ'
            ],
            'worldbuilding': [
                'Tam đại gia tộc U Minh: Hắc Nguyệt (suy tàn), Lôi Viêm (Lôi hệ), Hắc Thiết (chiến binh)',
                'Ngũ đại gia tộc nhỏ: Chu, Lý, Trần, Ngô, Triệu - Chu gia nổi tiếng chế tạo đan dược'
            ]
        })
    elif chapter_num == 3:
        summary.update({
            'key_events': [
                'Vân Nam bắt đầu tu luyện với tâm pháp Hắc Nguyệt bí điển do Thủy Tổ sáng tạo',
                'Ôn lại hệ thống cảnh giới tu luyện và phân tích thực lực các gia tộc',
                'Chuẩn bị cho việc tu luyện và phát triển sức mạnh'
            ],
            'character_development': [
                'Vân Nam thể hiện kiến thức sâu rộng về tu luyện và cảnh giới',
                'Quyết tâm đưa gia tộc Hắc Nguyệt trở lại thời hoàng kim'
            ],
            'worldbuilding': [
                'Hệ thống cảnh giới: Nguyên Đồ (1-10) → Nguyên Sĩ (11-20) → Nguyên Sư (21-30) → Đại Nguyên Sư (31-40) → Nguyên Tông (41-50)',
                'Thực lực hiện tại: Hắc Kình (Nguyên Sư đỉnh phong), Lôi Chấn (Nguyên Sư cấp 29), Hắc Nham (Nguyên Sư cấp 28)'
            ]
        })
    
    return summary

def create_better_summary():
    """Tạo file tóm tắt tốt hơn"""
    print("Đang tạo file tóm tắt cải thiện...")
    
    summary_content = []
    
    # Header
    summary_content.append("=" * 80)
    summary_content.append("HẮC ÁM CHI CHỦ - TÓM TẮT NỘI DUNG QUAN TRỌNG")
    summary_content.append("=" * 80)
    summary_content.append("")
    summary_content.append("📚 Tóm tắt ngắn gọn, tập trung vào các điểm then chốt")
    summary_content.append("🎯 Chỉ ghi lại những sự kiện ảnh hưởng trực tiếp đến cốt truyện")
    summary_content.append("⚡ Phát triển nhân vật và thông tin worldbuilding cần thiết")
    summary_content.append("")
    summary_content.append(f"🕒 Được tạo vào: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    summary_content.append("=" * 80)
    summary_content.append("")
    
    # Phân tích 3 chương đầu làm mẫu
    for chapter_num in range(1, 4):
        print(f"Đang phân tích Chương {chapter_num}...")
        summary = analyze_chapter_content(chapter_num)
        
        if summary:
            summary_content.append(f"CHƯƠNG {chapter_num}: {summary['title'].replace(f'Chương {chapter_num}: ', '')}")
            summary_content.append("=" * 60)
            
            if summary['setting']:
                summary_content.append(f"📍 {summary['setting']}")
                summary_content.append("")
            
            if summary['key_events']:
                summary_content.append("🔥 SỰ KIỆN CHÍNH:")
                for event in summary['key_events']:
                    summary_content.append(f"• {event}")
                summary_content.append("")
            
            if summary['character_development']:
                summary_content.append("👤 NHÂN VẬT:")
                for dev in summary['character_development']:
                    summary_content.append(f"• {dev}")
                summary_content.append("")
            
            if summary['worldbuilding']:
                summary_content.append("🌍 THẾ GIỚI:")
                for world in summary['worldbuilding']:
                    summary_content.append(f"• {world}")
                summary_content.append("")
            
            if summary['important_details']:
                summary_content.append("⭐ CHI TIẾT QUAN TRỌNG:")
                for detail in summary['important_details']:
                    summary_content.append(f"• {detail}")
                summary_content.append("")
            
            summary_content.append("-" * 60)
            summary_content.append("")
    
    # Thêm hướng dẫn cho các chương còn lại
    summary_content.append("📋 HƯỚNG DẪN CHO CÁC CHƯƠNG TIẾP THEO:")
    summary_content.append("")
    summary_content.append("Để tạo tóm tắt cho các chương 4-40, cần đọc và ghi lại:")
    summary_content.append("• Các sự kiện chính thay đổi tình hình")
    summary_content.append("• Phát triển sức mạnh của nhân vật")
    summary_content.append("• Mối quan hệ giữa các nhân vật")
    summary_content.append("• Thông tin mới về thế giới")
    summary_content.append("• Các bí mật được tiết lộ")
    summary_content.append("")
    summary_content.append("🎯 CÁC MẠCH TRUYỆN CẦN THEO DÕI:")
    summary_content.append("• Phát triển sức mạnh Hắc Ám Chi Nguyên của Vân Nam")
    summary_content.append("• Mối quan hệ tình cảm với Chu Tiểu Linh")
    summary_content.append("• Cạnh tranh và xung đột giữa các gia tộc")
    summary_content.append("• Chuẩn bị và tham gia kỳ thi Học Viện Tinh Võ")
    summary_content.append("• Khám phá bí mật về Khế Ước Bóng Đêm và Ảnh Giới")
    summary_content.append("• Âm mưu và thế lực thù địch")
    
    # Ghi file
    output_file = "Hac_Am_Chi_Chu_Tom_Tat_Cai_Thien.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(summary_content))
        
        print(f"✓ Đã tạo thành công file tóm tắt cải thiện: {output_file}")
        
        # Thống kê
        total_size = os.path.getsize(output_file)
        print(f"✓ Kích thước file: {total_size:,} bytes")
        
    except Exception as e:
        print(f"✗ Lỗi khi tạo file: {e}")

def main():
    create_better_summary()

if __name__ == "__main__":
    main()
