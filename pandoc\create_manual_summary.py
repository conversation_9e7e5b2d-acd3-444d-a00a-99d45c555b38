#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để tạo file tóm tắt thủ công với nội dung chính xác hơn
"""

import os
from datetime import datetime

def read_chapter(chapter_num):
    """Đọc nội dung một chương"""
    filename = f"Chương {chapter_num}.txt"
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        return content
    except:
        return ""

def create_manual_summary():
    """Tạo tóm tắt thủ công dựa trên việc đọc nội dung"""
    
    # Đọc một vài chương để tạo tóm tắt mẫu
    chapter_summaries = {
        1: {
            'title': 'Hắc Ám Thức Tỉnh Dưới Vòm Trời Vĩnh Dạ',
            'setting': '<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ố<PERSON> V<PERSON>nh Dạ',
            'key_events': [
                '<PERSON><PERSON> (16 tuổi) tham gia nghi thức thức tỉnh <PERSON>uyên <PERSON>',
                '<PERSON>ồi tưởng về chị gái <PERSON>ắc Vân <PERSON> thức tỉnh Hắc Ám Song Lang 3 năm trước',
                'Vân Nam thức tỉnh Hắc Ám Chi Nguyên và Hắc Nanh, ẩn giấu sức mạnh thật'
            ],
            'character_development': [
                'Vân Nam quyết định ẩn giấu thực lực để bảo vệ bản thân và gia tộc',
                'Hình thành kế hoạch sử dụng Khế Ước Bóng Đêm để xây dựng thế lực riêng'
            ],
            'worldbuilding': [
                'Giới thiệu Đế Quốc Vĩnh Dạ - vùng đất bóng tối ở cực Bắc',
                'Gia tộc Hắc Nguyệt từng huy hoàng với Thủy Tổ Hắc Thiên (Nguyên Tông)'
            ]
        },
        2: {
            'title': 'Tiệc Đêm U Minh, Rồng Ẩn Mình Chờ Thời',
            'setting': 'Thành U Minh, Phủ đệ gia tộc Hắc Nguyệt',
            'key_events': [
                'Gia tộc Hắc Nguyệt tổ chức tiệc mừng sau thức tỉnh của Vân Nam',
                'Các gia tộc Lôi Viêm và Hắc Thiết đến chúc mừng nhưng ẩn chứa ghen tị',
                'Bàn luận về kỳ thi tuyển chọn đệ tử Học Viện Tinh Võ sau 6 tháng'
            ],
            'character_development': [
                'Chu Tiểu Linh xuất hiện - thanh mai trúc mã của Vân Nam',
                'Vân Nam thể hiện sự bình tĩnh và khôn ngoan trong giao tiếp'
            ],
            'worldbuilding': [
                'Giới thiệu tam đại gia tộc U Minh: Hắc Nguyệt, Lôi Viêm, Hắc Thiết',
                'Học Viện Tinh Võ - nơi đào tạo Nguyên Giả tinh anh'
            ]
        },
        3: {
            'title': 'Sơ Khai Cảnh Giới, U Minh Ám Lưu',
            'setting': 'Phủ đệ Hắc Nguyệt, Thành U Minh',
            'key_events': [
                'Vân Nam bắt đầu tu luyện với tâm pháp Hắc Nguyệt bí điển',
                'Ôn lại hệ thống cảnh giới tu luyện từ Nguyên Đồ đến Nguyên Tông',
                'Phân tích thực lực các gia tộc trong U Minh Thành'
            ],
            'character_development': [
                'Vân Nam thể hiện sự hiểu biết sâu sắc về tu luyện',
                'Quyết tâm đưa gia tộc Hắc Nguyệt trở lại thời hoàng kim'
            ],
            'worldbuilding': [
                'Chi tiết hệ thống cảnh giới: Nguyên Đồ → Nguyên Sĩ → Nguyên Sư → Đại Nguyên Sư → Nguyên Tông',
                'So sánh thực lực các gia tộc: Hắc Kình (Nguyên Sư đỉnh phong), Lôi Chấn (Nguyên Sư cấp 29)'
            ]
        }
    }
    
    summary_content = []
    
    # Header
    summary_content.append("=" * 80)
    summary_content.append("HẮC ÁM CHI CHỦ - TÓM TẮT NỘI DUNG CHÍNH")
    summary_content.append("=" * 80)
    summary_content.append("")
    summary_content.append("📖 Tóm tắt các sự kiện quan trọng, mạch truyện chính")
    summary_content.append("🎯 Các chi tiết ảnh hưởng trực tiếp đến cốt truyện")
    summary_content.append("⚡ Phát triển nhân vật và worldbuilding quan trọng")
    summary_content.append("")
    summary_content.append(f"🕒 Được tạo thủ công vào: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    summary_content.append("=" * 80)
    summary_content.append("")
    
    # Tóm tắt các chương đã phân tích
    for chapter_num, info in chapter_summaries.items():
        summary_content.append(f"CHƯƠNG {chapter_num}: {info['title']}")
        summary_content.append("=" * 60)
        summary_content.append(f"📍 BỐI CẢNH: {info['setting']}")
        summary_content.append("")
        
        summary_content.append("🔥 SỰ KIỆN QUAN TRỌNG:")
        for i, event in enumerate(info['key_events'], 1):
            summary_content.append(f"   {i}. {event}")
        summary_content.append("")
        
        summary_content.append("👤 PHÁT TRIỂN NHÂN VẬT:")
        for i, dev in enumerate(info['character_development'], 1):
            summary_content.append(f"   {i}. {dev}")
        summary_content.append("")
        
        summary_content.append("🌍 WORLDBUILDING:")
        for i, world in enumerate(info['worldbuilding'], 1):
            summary_content.append(f"   {i}. {world}")
        summary_content.append("")
        summary_content.append("-" * 60)
        summary_content.append("")
    
    # Thêm ghi chú cho các chương còn lại
    summary_content.append("📝 GHI CHÚ:")
    summary_content.append("Các chương 4-40 cần được phân tích thủ công để tạo tóm tắt chính xác.")
    summary_content.append("Hiện tại chỉ có tóm tắt mẫu cho 3 chương đầu.")
    summary_content.append("")
    summary_content.append("🎯 CÁC YẾU TỐ QUAN TRỌNG CẦN THEO DÕI:")
    summary_content.append("- Phát triển sức mạnh của Vân Nam")
    summary_content.append("- Mối quan hệ với Chu Tiểu Linh")
    summary_content.append("- Cạnh tranh giữa các gia tộc")
    summary_content.append("- Kỳ thi Học Viện Tinh Võ")
    summary_content.append("- Bí mật về Hắc Ám Chi Nguyên")
    summary_content.append("- Khế Ước Bóng Đêm và Ảnh Giới")
    
    # Ghi file
    output_file = "Hac_Am_Chi_Chu_Tom_Tat_Chinh_Xac.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(summary_content))
        
        print(f"✓ Đã tạo thành công file tóm tắt chính xác: {output_file}")
        
        # Thống kê
        total_size = os.path.getsize(output_file)
        print(f"✓ Kích thước file: {total_size:,} bytes")
        
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = len(f.readlines())
        print(f"✓ Tổng số dòng: {lines:,}")
        
    except Exception as e:
        print(f"✗ Lỗi khi tạo file tóm tắt: {e}")

def main():
    create_manual_summary()

if __name__ == "__main__":
    main()
