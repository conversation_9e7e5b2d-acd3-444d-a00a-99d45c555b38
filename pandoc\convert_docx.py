#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để chuyển đổi file Word (.docx) sang text (.txt)
Sử dụng thư viện python-docx
"""

import sys
import os

def install_requirements():
    """Cài đặt các thư viện cần thiết"""
    try:
        import docx
        print("✓ python-docx đã được cài đặt")
        return True
    except ImportError:
        print("Đang cài đặt python-docx...")
        os.system("pip install python-docx")
        try:
            import docx
            print("✓ Cài đặt python-docx thành công")
            return True
        except ImportError:
            print("✗ Không thể cài đặt python-docx")
            return False

def convert_docx_to_txt(docx_path, txt_path):
    """Chuyển đổi file .docx sang .txt"""
    try:
        from docx import Document
        
        # Đọc file Word
        doc = Document(docx_path)
        
        # Trích xuất text
        full_text = []
        for paragraph in doc.paragraphs:
            full_text.append(paragraph.text)
        
        # Ghi vào file text
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(full_text))
        
        print(f"✓ Đã chuyển đổi: {docx_path} -> {txt_path}")
        return True
        
    except Exception as e:
        print(f"✗ Lỗi khi chuyển đổi: {e}")
        return False

def convert_all_chapters():
    """Chuyển đổi tất cả các chương từ 2 đến 40"""
    success_count = 0
    fail_count = 0

    print("Bắt đầu chuyển đổi các chương từ 2 đến 40...")
    print("=" * 50)

    for i in range(2, 41):  # Từ chương 2 đến 40
        docx_file = f"../Chương {i}.docx"
        txt_file = f"Chương {i}.txt"

        if os.path.exists(docx_file):
            if convert_docx_to_txt(docx_file, txt_file):
                success_count += 1
            else:
                fail_count += 1
        else:
            print(f"✗ Không tìm thấy file: {docx_file}")
            fail_count += 1

    print("=" * 50)
    print(f"Hoàn thành! Thành công: {success_count}, Thất bại: {fail_count}")

def main():
    # Cài đặt thư viện nếu cần
    if not install_requirements():
        return

    # Chuyển đổi tất cả các chương
    convert_all_chapters()

if __name__ == "__main__":
    main()
